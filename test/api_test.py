#!/usr/bin/env python3
"""
API Test Script for Multi-Modal RAG Application

This script tests all API endpoints to ensure they are working correctly.
Run this after starting the application to verify functionality.
"""

import sys
import time
from pathlib import Path

import requests


class APITester:
    """Test class for API endpoints."""

    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()

    def test_health_endpoint(self):
        """Test the health check endpoint."""
        print("🔍 Testing Health Endpoint...")

        try:
            response = self.session.get(f"{self.base_url}/health/")

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health check passed")
                print(f"   Status: {data.get('status')}")
                print(f"   Version: {data.get('version')}")
                print(f"   Vector DB: {data.get('vector_db_status')}")
                print(f"   Models Loaded: {data.get('models_loaded')}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False

    def test_root_endpoint(self):
        """Test the root endpoint."""
        print("\n🔍 Testing Root Endpoint...")

        try:
            response = self.session.get(f"{self.base_url}/")

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Root endpoint accessible")
                print(f"   Message: {data.get('message')}")
                print(f"   Version: {data.get('version')}")
                return True
            else:
                print(f"❌ Root endpoint failed: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Root endpoint error: {e}")
            return False

    def test_upload_endpoint(self, pdf_path=None):
        """Test the upload endpoint."""
        print("\n🔍 Testing Upload Endpoint...")

        # Use default test file if none provided
        if pdf_path is None:
            pdf_path = "resource/pdf/attention_is_all_you_need.pdf"

        if not Path(pdf_path).exists():
            print(f"❌ Test PDF file not found: {pdf_path}")
            return False

        try:
            with open(pdf_path, "rb") as f:
                files = {"file": (Path(pdf_path).name, f, "application/pdf")}

                print(f"   Uploading: {Path(pdf_path).name}")
                response = self.session.post(
                    f"{self.base_url}/upload/",
                    files=files,
                    timeout=120,  # Allow time for processing
                )

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Upload successful")
                print(f"   Filename: {data.get('filename')}")
                print(f"   Text chunks: {data.get('text_chunks')}")
                print(f"   Image chunks: {data.get('img_chunks')}")
                print(f"   Total chunks: {data.get('total_chunks')}")
                return True
            else:
                print(f"❌ Upload failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Upload error: {e}")
            return False

    def test_query_endpoint(self, query="what is the multi-head attention?"):
        """Test the query endpoint."""
        print("\n🔍 Testing Query Endpoint...")

        try:
            data = {"query": query}

            print(f"   Query: {query}")
            response = self.session.post(
                f"{self.base_url}/query/", data=data, timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                print(f"✅ Query successful")
                print(f"   Answer: {result.get('answer')[:100]}...")
                print(f"   Retrieved chunks: {result.get('retrieved_chunks')}")
                return True
            else:
                print(f"❌ Query failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Query error: {e}")
            return False

    def test_invalid_requests(self):
        """Test error handling with invalid requests."""
        print("\n🔍 Testing Error Handling...")

        tests_passed = 0
        total_tests = 3

        # Test 1: Invalid file upload
        try:
            response = self.session.post(f"{self.base_url}/upload/")
            if response.status_code == 422:  # Validation error
                print("✅ Invalid upload properly rejected")
                tests_passed += 1
            else:
                print(f"❌ Invalid upload not properly handled: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing invalid upload: {e}")

        # Test 2: Empty query
        try:
            response = self.session.post(f"{self.base_url}/query/", data={"query": ""})
            if response.status_code == 422:  # Validation error
                print("✅ Empty query properly rejected")
                tests_passed += 1
            else:
                print(f"❌ Empty query not properly handled: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing empty query: {e}")

        # Test 3: Non-existent endpoint
        try:
            response = self.session.get(f"{self.base_url}/nonexistent/")
            if response.status_code == 404:
                print("✅ Non-existent endpoint properly returns 404")
                tests_passed += 1
            else:
                print(
                    f"❌ Non-existent endpoint handling unexpected: {response.status_code}"
                )
        except Exception as e:
            print(f"❌ Error testing non-existent endpoint: {e}")

        return tests_passed == total_tests

    def run_all_tests(self):
        """Run all API tests."""
        print("🚀 Starting API Tests for Multi-Modal RAG Application")
        print("=" * 60)

        tests = [
            ("Health Check", self.test_health_endpoint),
            ("Root Endpoint", self.test_root_endpoint),
            ("Upload Endpoint", self.test_upload_endpoint),
            ("Query Endpoint", self.test_query_endpoint),
            ("Error Handling", self.test_invalid_requests),
        ]

        results = {}

        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ Test '{test_name}' failed with exception: {e}")
                results[test_name] = False

            time.sleep(1)  # Brief pause between tests

        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)

        passed = 0
        total = len(tests)

        for test_name, result in results.items():
            status = "PASSED" if result else "FAILED"
            print(f"{test_name}: {status}")
            if result:
                passed += 1

        print(f"\nOverall: {passed}/{total} tests passed")

        if passed == total:
            print("🎉 ALL API TESTS PASSED! The application is working correctly.")
            return True
        else:
            print("❌ Some API tests failed. Please check the application and logs.")
            return False


def main():
    """Main function to run API tests."""
    import argparse

    parser = argparse.ArgumentParser(description="Test Multi-Modal RAG API endpoints")
    parser.add_argument(
        "--url",
        default="http://localhost:8000",
        help="Base URL of the API (default: http://localhost:8000)",
    )
    parser.add_argument("--pdf", help="Path to PDF file for testing upload")

    args = parser.parse_args()

    # Check if server is running
    try:
        response = requests.get(args.url, timeout=5)
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to API at {args.url}")
        print("   Make sure the application is running with: python main_new.py")
        sys.exit(1)

    # Run tests
    tester = APITester(args.url)
    success = tester.run_all_tests()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
