# Multi-Modal RAG API

A powerful Retrieval-Augmented Generation (RAG) system that processes PDF documents and provides intelligent question-answering capabilities using state-of-the-art AI models.

## 🚀 Features

- **Multi-Modal Processing**: Extract and process both text and images from PDFs
- **Intelligent Chunking**: Smart text splitting with context preservation
- **Semantic Search**: Vector-based similarity search using Cohere embeddings
- **AI-Powered Answers**: Context-aware responses using Cohere LLM
- **Image Understanding**: AI-powered image summarization using Google Generative AI
- **Scalable Storage**: Persistent vector storage with ChromaDB
- **Production Ready**: Comprehensive error handling, logging, and monitoring
- **RESTful API**: FastAPI with automatic OpenAPI documentation

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PDF Upload    │───▶│  Text & Image   │───▶│   Chunking &    │
│                 │    │   Extraction    │    │   Embedding     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  User Query     │───▶│  Similarity     │◀───│  Vector Store   │
│                 │    │    Search       │    │   (ChromaDB)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   LLM Answer    │◀───│   Retrieved     │
│   Generation    │    │   Context       │
└─────────────────┘    └─────────────────┘
```

## 🛠️ Technology Stack

- **Framework**: FastAPI with async support
- **PDF Processing**: PyMuPDF for text and image extraction
- **Embeddings**: Cohere embed-english-v3.0
- **LLM**: Cohere command-r-plus
- **Vector Database**: ChromaDB for similarity search
- **Image AI**: Google Generative AI (Gemini 2.5 Flash)
- **Validation**: Pydantic for data validation
- **Logging**: Structured logging with configurable levels

## 📋 Prerequisites

- Python 3.8+
- API Keys:
  - Cohere API Key (for embeddings and LLM)
  - Google AI API Key (for image processing)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/multi-modal-rag.git
cd multi-modal-rag
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` file:

```env
# API Keys
COHERE_API_KEY=your_cohere_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Application Settings
DEBUG=True
LOG_LEVEL=INFO

# Model Settings
CHUNK_SIZE=400
CHUNK_OVERLAP=50
RETRIEVAL_K=3
```

### 4. Run the Application

```bash
python main_new.py
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

## 📖 API Usage

### Standard Format APIs (Recommended)

The API supports a standardized two-part request/response format with header containing traceId for request tracking.

#### Upload a PDF Document (Standard Response)

```bash
curl -X POST "http://localhost:8000/upload/standard" \
  -F "file=@your_document.pdf" \
  -F "traceId=your-trace-id-123"
```

Response:
```json
{
  "header": {
    "traceId": "your-trace-id-123"
  },
  "payload": {
    "message": "文档解析并入库成功",
    "filename": "your_document.pdf",
    "text_chunks": 25,
    "img_chunks": 3,
    "total_chunks": 28
  }
}
```

#### Query the Knowledge Base (Standard Format)

```bash
curl -X POST "http://localhost:8000/query/standard" \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "traceId": "your-trace-id-456"
    },
    "payload": {
      "query": "What is the main topic of the document?"
    }
  }'
```

Response:
```json
{
  "header": {
    "traceId": "your-trace-id-456"
  },
  "payload": {
    "answer": "Based on the uploaded document, the main topic is...",
    "query": "What is the main topic of the document?",
    "retrieved_chunks": 3
  }
}
```

### Legacy Format APIs (Backward Compatibility)

#### Upload a PDF Document (Legacy)

```bash
curl -X POST "http://localhost:8000/upload/" \
  -F "file=@your_document.pdf"
```

Response:
```json
{
  "message": "文档解析并入库成功",
  "filename": "your_document.pdf",
  "text_chunks": 25,
  "img_chunks": 3,
  "total_chunks": 28
}
```

#### Query the Knowledge Base (Legacy)

```bash
curl -X POST "http://localhost:8000/query/" \
  -F "query=What is the main topic of the document?"
```

Response:
```json
{
  "answer": "Based on the uploaded document, the main topic is...",
  "query": "What is the main topic of the document?",
  "retrieved_chunks": 3
}
```

#### Check System Health (Standard Format)

```bash
curl -X GET "http://localhost:8000/health/standard"
```

Response:
```json
{
  "header": {
    "traceId": "auto-generated-trace-id"
  },
  "payload": {
    "status": "healthy",
    "version": "1.0.0",
    "vector_db_status": "available",
    "models_loaded": true
  }
}
```

#### Check System Health (Legacy)

```bash
curl -X GET "http://localhost:8000/health/"
```

Response:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "vector_db_status": "available",
  "models_loaded": true
}
```

## 🏗️ Project Structure

```
multi-modal-rag/
├── app/
│   ├── config/          # Configuration management
│   │   ├── __init__.py
│   │   └── settings.py
│   ├── models/          # Pydantic models
│   │   ├── __init__.py
│   │   └── schemas.py
│   ├── routers/         # API route handlers
│   │   ├── __init__.py
│   │   ├── upload.py
│   │   ├── query.py
│   │   └── health.py
│   ├── services/        # Business logic
│   │   ├── __init__.py
│   │   ├── document_service.py
│   │   ├── vector_service.py
│   │   └── llm_service.py
│   ├── utils/           # Utilities
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── exceptions.py
│   │   ├── validators.py
│   │   └── cache.py
│   ├── __init__.py
│   └── main.py          # FastAPI application
├── resource/
│   └── pdf/             # Test PDF files
├── requirements.txt     # Python dependencies
├── pyproject.toml      # Project configuration
├── .env.example        # Environment template
├── main_new.py         # Application entry point
└── README.md           # This file
```

## ⚙️ Configuration

The application uses environment variables for configuration. Key settings:

| Variable | Description | Default |
|----------|-------------|---------|
| `COHERE_API_KEY` | Cohere API key for embeddings and LLM | Required |
| `GOOGLE_API_KEY` | Google AI API key for image processing | Required |
| `MAX_FILE_SIZE_MB` | Maximum upload file size in MB | 50 |
| `CHUNK_SIZE` | Text chunk size for processing | 400 |
| `CHUNK_OVERLAP` | Overlap between text chunks | 50 |
| `RETRIEVAL_K` | Number of chunks to retrieve | 3 |
| `LOG_LEVEL` | Logging level (DEBUG, INFO, WARNING, ERROR) | INFO |

## 🧪 Testing

Run the core functionality tests:

```bash
python simple_test.py
```

This will test:
- Directory structure
- Configuration loading
- PDF text extraction
- Text splitting functionality

## 🔧 Development

### Code Structure

The application follows a layered architecture:

- **Routers**: Handle HTTP requests and responses
- **Services**: Contain business logic and external API interactions
- **Models**: Define data structures and validation
- **Utils**: Provide common utilities and helpers
- **Config**: Manage application configuration

### Adding New Features

1. Define data models in `app/models/schemas.py`
2. Implement business logic in appropriate service
3. Create API endpoints in routers
4. Add configuration options if needed
5. Update documentation

## 📊 Monitoring and Logging

The application provides comprehensive logging and monitoring:

- **Structured Logging**: JSON-formatted logs with timestamps
- **Health Checks**: Monitor system status and dependencies
- **Error Handling**: Graceful error handling with detailed messages
- **Performance Metrics**: Track processing times and success rates

## 🚀 Deployment

### Docker Deployment (Recommended)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "main_new.py"]
```

### Environment Variables for Production

```env
DEBUG=False
LOG_LEVEL=WARNING
MAX_FILE_SIZE_MB=100
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue on GitHub
- Check the API documentation at `/docs`
- Review the logs for error details

## 💡 Usage Examples

### Python Client Example (Standard Format)

```python
import requests
import uuid

# Upload a document with standardized response
trace_id = str(uuid.uuid4())
with open("document.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8000/upload/standard",
        files={"file": f},
        data={"traceId": trace_id}
    )
result = response.json()
print(f"Trace ID: {result['header']['traceId']}")
print(f"Upload result: {result['payload']}")

# Query the document with standardized format
trace_id = str(uuid.uuid4())
query_data = {
    "header": {"traceId": trace_id},
    "payload": {"query": "What are the main findings?"}
}
response = requests.post(
    "http://localhost:8000/query/standard",
    json=query_data
)
result = response.json()
print(f"Trace ID: {result['header']['traceId']}")
print(f"Answer: {result['payload']['answer']}")
```

### Python Client Example (Legacy Format)

```python
import requests

# Upload a document
with open("document.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8000/upload/",
        files={"file": f}
    )
print(response.json())

# Query the document
response = requests.post(
    "http://localhost:8000/query/",
    data={"query": "What are the main findings?"}
)
print(response.json())
```

### JavaScript/Node.js Example (Standard Format)

```javascript
const FormData = require('form-data');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Upload document with standardized response
const uploadForm = new FormData();
uploadForm.append('file', fs.createReadStream('document.pdf'));
uploadForm.append('traceId', uuidv4());

fetch('http://localhost:8000/upload/standard', {
    method: 'POST',
    body: uploadForm
}).then(response => response.json())
  .then(data => {
    console.log('Trace ID:', data.header.traceId);
    console.log('Upload result:', data.payload);
  });

// Query document with standardized format
const queryData = {
    header: { traceId: uuidv4() },
    payload: { query: 'What are the main findings?' }
};

fetch('http://localhost:8000/query/standard', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(queryData)
}).then(response => response.json())
  .then(data => {
    console.log('Trace ID:', data.header.traceId);
    console.log('Answer:', data.payload.answer);
  });
```

## 🔮 Roadmap

- [ ] Support for additional file formats (DOCX, TXT)
- [ ] Batch processing capabilities
- [ ] Advanced search filters
- [ ] User authentication and authorization
- [ ] Caching layer for improved performance
- [ ] Metrics and analytics dashboard
