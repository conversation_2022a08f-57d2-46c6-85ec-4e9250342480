[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "multi-modal-rag"
version = "1.0.0"
description = "A multi-modal RAG (Retrieval-Augmented Generation) API using FastAPI"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "PyMuPDF>=1.23.8",
    "Pillow>=10.1.0",
    "google-generativeai>=0.3.2",
    "langchain>=0.0.352",
    "langchain-cohere>=0.0.8",
    "langchain-community>=0.0.10",
    "chromadb>=0.4.18",
    "tiktoken>=0.5.2",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "aiofiles>=23.2.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/multi-modal-rag"
Repository = "https://github.com/yourusername/multi-modal-rag"
Issues = "https://github.com/yourusername/multi-modal-rag/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
