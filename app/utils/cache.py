"""Caching utilities."""

import hashlib
import json
from typing import Any, Optional, Dict
from functools import wraps
from cachetools import TTLCache
from app.utils.logger import logger


class MemoryCache:
    """Simple in-memory cache using cachetools."""
    
    def __init__(self, maxsize: int = 1000, ttl: int = 3600):
        self.cache = TTLCache(maxsize=maxsize, ttl=ttl)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        return self.cache.get(key)
    
    def set(self, key: str, value: Any) -> None:
        """Set value in cache."""
        self.cache[key] = value
    
    def delete(self, key: str) -> None:
        """Delete value from cache."""
        self.cache.pop(key, None)
    
    def clear(self) -> None:
        """Clear all cache."""
        self.cache.clear()
    
    def size(self) -> int:
        """Get cache size."""
        return len(self.cache)


# Global cache instances
query_cache = MemoryCache(maxsize=500, ttl=1800)  # 30 minutes
embedding_cache = MemoryCache(maxsize=1000, ttl=7200)  # 2 hours


def cache_key(*args, **kwargs) -> str:
    """Generate cache key from arguments."""
    key_data = {
        'args': args,
        'kwargs': sorted(kwargs.items())
    }
    key_str = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_str.encode()).hexdigest()


def cached_query(cache_instance: MemoryCache = query_cache):
    """Decorator for caching query results."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            key = cache_key(*args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_instance.get(key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {key[:16]}...")
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache_instance.set(key, result)
            logger.debug(f"Cache miss, stored result for key: {key[:16]}...")
            
            return result
        return wrapper
    return decorator


def cached_embedding(cache_instance: MemoryCache = embedding_cache):
    """Decorator for caching embedding results."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key = cache_key(*args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_instance.get(key)
            if cached_result is not None:
                logger.debug(f"Embedding cache hit for key: {key[:16]}...")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_instance.set(key, result)
            logger.debug(f"Embedding cache miss, stored result for key: {key[:16]}...")
            
            return result
        return wrapper
    return decorator
