"""Validation utilities."""

import os

from fastapi import UploadFile

from app.config import settings
from app.utils.exceptions import ValidationError


def validate_file_type(file: UploadFile) -> None:
    """Validate uploaded file type."""
    if not file.filename:
        raise ValidationError("Filename is required", "MISSING_FILENAME")

    file_extension = os.path.splitext(file.filename)[1].lower().lstrip(".")

    if file_extension not in settings.allowed_file_types:
        raise ValidationError(
            f"File type '{file_extension}' not allowed. "
            f"Allowed types: {', '.join(settings.allowed_file_types)}",
            "INVALID_FILE_TYPE",
        )


def validate_file_size(file: UploadFile) -> None:
    """Validate uploaded file size."""
    if not hasattr(file.file, "seek") or not hasattr(file.file, "tell"):
        # If we can't determine size, skip validation
        return

    # Get current position
    current_pos = file.file.tell()

    # Seek to end to get size
    file.file.seek(0, 2)
    file_size = file.file.tell()

    # Reset to original position
    file.file.seek(current_pos)

    if file_size > settings.max_file_size_bytes:
        raise ValidationError(
            f"File size ({file_size / 1024 / 1024:.2f} MB) exceeds maximum "
            f"allowed size ({settings.max_file_size_mb} MB)",
            "FILE_TOO_LARGE",
        )


def validate_upload_file(file: UploadFile) -> None:
    """Validate uploaded file."""
    validate_file_type(file)
    validate_file_size(file)


def validate_api_keys() -> None:
    """Validate that required API keys are present."""
    missing_keys = []

    if not settings.cohere_api_key:
        missing_keys.append("COHERE_API_KEY")

    if not settings.google_api_key:
        missing_keys.append("GOOGLE_API_KEY")

    if missing_keys:
        raise ValidationError(
            f"Missing required API keys: {', '.join(missing_keys)}", "MISSING_API_KEYS"
        )
