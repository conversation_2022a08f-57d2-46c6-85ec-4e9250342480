"""Utilities module."""

from .cache import cached_embedding, cached_query, embedding_cache, query_cache
from .exceptions import (
    ConfigurationError,
    FileProcessingError,
    ModelError,
    MultiModalRAGException,
    ValidationError,
    VectorStoreError,
)
from .logger import get_trace_id, logger, set_trace_id, setup_logger
from .validators import validate_api_keys, validate_upload_file

__all__ = [
    "logger",
    "setup_logger",
    "set_trace_id",
    "get_trace_id",
    "MultiModalRAGException",
    "FileProcessingError",
    "VectorStoreError",
    "ModelError",
    "ValidationError",
    "ConfigurationError",
    "validate_upload_file",
    "validate_api_keys",
    "query_cache",
    "embedding_cache",
    "cached_query",
    "cached_embedding",
]
