"""Logging configuration with trace ID support."""

import logging
import sys
from contextvars import <PERSON>text<PERSON><PERSON>
from typing import Optional

from app.config import settings

# Context variable for trace ID
trace_id_context: ContextVar[Optional[str]] = ContextVar("trace_id", default=None)


class TraceIdFormatter(logging.Formatter):
    """Custom formatter that includes trace ID in log messages."""

    def format(self, record):
        trace_id = trace_id_context.get()
        if trace_id:
            record.trace_id = trace_id
        else:
            record.trace_id = "N/A"
        return super().format(record)


def set_trace_id(trace_id: str) -> None:
    """Set the trace ID for the current context."""
    trace_id_context.set(trace_id)


def get_trace_id() -> Optional[str]:
    """Get the current trace ID."""
    return trace_id_context.get()


def setup_logger(name: Optional[str] = None) -> logging.Logger:
    """Setup and configure logger."""

    logger = logging.getLogger(name or __name__)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Set log level
    log_level = getattr(logging, settings.log_level.upper(), logging.INFO)
    logger.setLevel(log_level)

    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(log_level)

    # Create formatter with trace ID support
    formatter = TraceIdFormatter(
        fmt="%(asctime)s - %(name)s - %(levelname)s - [%(trace_id)s] - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(handler)

    return logger


# Global logger instance
logger = setup_logger("multi_modal_rag")
