"""Custom exceptions."""


class MultiModalRAGException(Exception):
    """Base exception for Multi-Modal RAG application."""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class FileProcessingError(MultiModalRAGException):
    """Exception raised when file processing fails."""
    pass


class VectorStoreError(MultiModalRAGException):
    """Exception raised when vector store operations fail."""
    pass


class ModelError(MultiModalRAGException):
    """Exception raised when model operations fail."""
    pass


class ValidationError(MultiModalRAGException):
    """Exception raised when validation fails."""
    pass


class ConfigurationError(MultiModalRAGException):
    """Exception raised when configuration is invalid."""
    pass
