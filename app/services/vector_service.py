"""Vector store service for managing embeddings and retrieval."""

import os
from typing import List, Optional
from langchain_cohere import CohereEmbeddings
from langchain_community.vectorstores import Chroma
from langchain_core.documents import Document

from app.config import settings
from app.utils import logger, VectorStoreError


class VectorService:
    """Service for managing vector store operations."""
    
    def __init__(self):
        self._embedding_model: Optional[CohereEmbeddings] = None
        self._vectorstore: Optional[Chroma] = None
    
    @property
    def embedding_model(self) -> CohereEmbeddings:
        """Get or create embedding model."""
        if self._embedding_model is None:
            try:
                self._embedding_model = CohereEmbeddings(
                    model=settings.embedding_model,
                    cohere_api_key=settings.cohere_api_key,
                )
                logger.info(f"Initialized embedding model: {settings.embedding_model}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding model: {e}")
                raise VectorStoreError(f"Failed to initialize embedding model: {e}")
        
        return self._embedding_model
    
    @property
    def vectorstore(self) -> Optional[Chroma]:
        """Get existing vector store if available."""
        if self._vectorstore is None and os.path.exists(settings.vector_db_dir):
            try:
                self._vectorstore = Chroma(
                    persist_directory=settings.vector_db_dir,
                    collection_name=settings.collection_name,
                    embedding_function=self.embedding_model,
                )
                logger.info("Loaded existing vector store")
            except Exception as e:
                logger.error(f"Failed to load vector store: {e}")
                raise VectorStoreError(f"Failed to load vector store: {e}")
        
        return self._vectorstore
    
    def create_vectorstore(self, documents: List[Document]) -> Chroma:
        """Create new vector store with documents."""
        try:
            logger.info(f"Creating vector store with {len(documents)} documents")
            
            self._vectorstore = Chroma.from_documents(
                documents=documents,
                collection_name=settings.collection_name,
                embedding=self.embedding_model,
                persist_directory=settings.vector_db_dir,
            )
            
            self._vectorstore.persist()
            logger.info("Vector store created and persisted successfully")
            
            return self._vectorstore
            
        except Exception as e:
            logger.error(f"Failed to create vector store: {e}")
            raise VectorStoreError(f"Failed to create vector store: {e}")
    
    def add_documents(self, documents: List[Document]) -> None:
        """Add documents to existing vector store."""
        if not self.vectorstore:
            raise VectorStoreError("No vector store available. Create one first.")
        
        try:
            logger.info(f"Adding {len(documents)} documents to vector store")
            self.vectorstore.add_documents(documents)
            self.vectorstore.persist()
            logger.info("Documents added successfully")
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise VectorStoreError(f"Failed to add documents: {e}")
    
    def similarity_search(self, query: str, k: int = None) -> List[Document]:
        """Perform similarity search."""
        if not self.vectorstore:
            raise VectorStoreError("No vector store available")
        
        k = k or settings.retrieval_k
        
        try:
            logger.info(f"Performing similarity search for query: '{query[:50]}...' with k={k}")
            
            retriever = self.vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": k}
            )
            
            docs = retriever.invoke(query)
            logger.info(f"Retrieved {len(docs)} documents")
            
            return docs
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {e}")
            raise VectorStoreError(f"Failed to perform similarity search: {e}")
    
    def get_collection_count(self) -> int:
        """Get number of documents in collection."""
        if not self.vectorstore:
            return 0
        
        try:
            return self.vectorstore._collection.count()
        except Exception as e:
            logger.warning(f"Failed to get collection count: {e}")
            return 0
    
    def is_available(self) -> bool:
        """Check if vector store is available."""
        return self.vectorstore is not None


# Global vector service instance
vector_service = VectorService()
