"""Document processing service for PDF and image handling."""

import io
import os
from typing import Any, Dict, List, Optional, Tuple

import fitz  # PyMuPDF
from google import genai
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from PIL import Image

from app.config import settings
from app.utils import FileProcessingError, ModelError, logger


class DocumentService:
    """Service for processing documents and extracting content."""

    def __init__(self):
        self._genai_client: Optional[genai.Client] = None
        self._text_splitter: Optional[RecursiveCharacterTextSplitter] = None

    @property
    def genai_client(self) -> genai.Client:
        """Get or create Google Generative AI client."""
        if self._genai_client is None:
            try:
                self._genai_client = genai.Client(api_key=settings.google_api_key)
                logger.info("Initialized Google Generative AI client")
            except Exception as e:
                logger.error(f"Failed to initialize Google AI client: {e}")
                raise ModelError(f"Failed to initialize Google AI client: {e}")

        return self._genai_client

    @property
    def text_splitter(self) -> RecursiveCharacterTextSplitter:
        """Get or create text splitter."""
        if self._text_splitter is None:
            self._text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
                chunk_size=settings.chunk_size, chunk_overlap=settings.chunk_overlap
            )
            logger.info(
                f"Initialized text splitter with chunk_size={settings.chunk_size}"
            )

        return self._text_splitter

    def extract_pdf_content(
        self, file_bytes: bytes, filename: str
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """Extract text and images from PDF."""
        try:
            text_data = []
            image_paths = []

            # Create directory for extracted images
            image_dir = os.path.join(settings.extracted_images_dir, filename)
            os.makedirs(image_dir, exist_ok=True)

            logger.info(f"Processing PDF: {filename}")

            with fitz.open(stream=file_bytes, filetype="pdf") as pdf_file:
                for page_number in range(len(pdf_file)):
                    page = pdf_file[page_number]

                    # Extract text
                    text = page.get_text().strip()
                    if text:  # Only add non-empty text
                        text_data.append(
                            {"content": text, "page": page_number + 1, "type": "text"}
                        )

                    # Extract images
                    images = page.get_images(full=True)
                    for image_index, img in enumerate(images):
                        try:
                            xref = img[0]
                            base_image = pdf_file.extract_image(xref)
                            image_bytes = base_image["image"]
                            image_ext = base_image["ext"]

                            # Save image
                            image_filename = (
                                f"page_{page_number+1}_img_{image_index+1}.{image_ext}"
                            )
                            image_path = os.path.join(image_dir, image_filename)

                            image = Image.open(io.BytesIO(image_bytes))
                            image.save(image_path)
                            image_paths.append(image_path)

                            logger.debug(f"Extracted image: {image_path}")

                        except Exception as e:
                            logger.warning(
                                f"Failed to extract image {image_index} from page {page_number}: {e}"
                            )
                            continue

            logger.info(
                f"Extracted {len(text_data)} text sections and {len(image_paths)} images"
            )
            return text_data, image_paths

        except Exception as e:
            logger.error(f"Failed to extract PDF content: {e}")
            raise FileProcessingError(f"Failed to extract PDF content: {e}")

    def summarize_images(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """Generate summaries for images using Google Generative AI."""
        image_summaries = []

        prompt = (
            "You are an assistant tasked with summarizing tables, images and text for retrieval. "
            "These summaries will be embedded and used to retrieve the raw text or table elements. "
            "Give a concise summary of the table or text that is well optimized for retrieval. "
            "Table or text or image:"
        )

        for image_path in image_paths:
            try:
                logger.debug(f"Summarizing image: {image_path}")

                image = Image.open(image_path)
                response = self.genai_client.models.generate_content(
                    model="gemini-2.5-flash",
                    contents=[image, prompt],
                )

                image_summaries.append(
                    {
                        "content": response.text,
                        "source": os.path.basename(image_path),
                        "type": "image_summary",
                    }
                )

                logger.debug(f"Generated summary for {image_path}")

            except Exception as e:
                logger.warning(f"Failed to summarize image {image_path}: {e}")
                continue

        logger.info(f"Generated {len(image_summaries)} image summaries")
        return image_summaries

    def create_documents(
        self, text_data: List[Dict[str, Any]], image_summaries: List[Dict[str, Any]]
    ) -> List[Document]:
        """Create LangChain documents from text and image data."""
        documents = []

        # Create documents from text
        for item in text_data:
            doc = Document(
                page_content=item["content"],
                metadata={"source": item.get("page", "unknown"), "type": item["type"]},
            )
            documents.append(doc)

        # Create documents from image summaries
        for item in image_summaries:
            doc = Document(
                page_content=item["content"],
                metadata={"source": item["source"], "type": item["type"]},
            )
            documents.append(doc)

        logger.info(f"Created {len(documents)} documents")
        return documents

    def split_documents(self, documents: List[Document]) -> List[Document]:
        """Split documents into chunks."""
        try:
            split_docs = self.text_splitter.split_documents(documents)
            logger.info(
                f"Split {len(documents)} documents into {len(split_docs)} chunks"
            )
            return split_docs

        except Exception as e:
            logger.error(f"Failed to split documents: {e}")
            raise FileProcessingError(f"Failed to split documents: {e}")

    def process_pdf(
        self, file_bytes: bytes, filename: str
    ) -> Tuple[List[Document], Dict[str, int]]:
        """Complete PDF processing pipeline."""
        try:
            # Extract content
            text_data, image_paths = self.extract_pdf_content(file_bytes, filename)

            # Summarize images
            image_summaries = self.summarize_images(image_paths)

            # Create documents
            documents = self.create_documents(text_data, image_summaries)

            # Split documents
            split_documents = self.split_documents(documents)

            # Calculate statistics
            text_chunks = len(
                [d for d in split_documents if d.metadata.get("type") == "text"]
            )
            image_chunks = len(
                [
                    d
                    for d in split_documents
                    if d.metadata.get("type") == "image_summary"
                ]
            )

            stats = {
                "text_chunks": text_chunks,
                "img_chunks": image_chunks,
                "total_chunks": len(split_documents),
            }

            logger.info(f"PDF processing completed: {stats}")
            return split_documents, stats

        except Exception as e:
            logger.error(f"Failed to process PDF: {e}")
            raise FileProcessingError(f"Failed to process PDF: {e}")


# Global document service instance
document_service = DocumentService()
