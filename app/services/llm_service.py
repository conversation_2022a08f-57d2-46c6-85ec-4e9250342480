"""LLM service for generating responses."""

from typing import List, Optional
from langchain_cohere import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.documents import Document
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from app.config import settings
from app.utils import logger, ModelError


class LLMService:
    """Service for managing LLM operations."""
    
    def __init__(self):
        self._llm: Optional[ChatCohere] = None
        self._prompt_template: Optional[ChatPromptTemplate] = None
        self._chain = None
    
    @property
    def llm(self) -> ChatCohere:
        """Get or create LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatCohere(
                    model=settings.llm_model,
                    temperature=settings.llm_temperature,
                    cohere_api_key=settings.cohere_api_key,
                )
                logger.info(f"Initialized LLM: {settings.llm_model}")
            except Exception as e:
                logger.error(f"Failed to initialize LLM: {e}")
                raise ModelError(f"Failed to initialize LLM: {e}")
        
        return self._llm
    
    @property
    def prompt_template(self) -> ChatPromptTemplate:
        """Get or create prompt template."""
        if self._prompt_template is None:
            system_message = (
                "You are an assistant for question-answering tasks. "
                "Answer the question based upon the provided documents and your knowledge. "
                "Use three-to-five sentences maximum and keep the answer concise. "
                "If the documents don't contain relevant information, say so clearly."
            )
            
            self._prompt_template = ChatPromptTemplate.from_messages([
                ("system", system_message),
                ("human", 
                 "Retrieved documents: \n\n <docs>{documents}</docs> \n\n "
                 "User question: <question>{question}</question>"),
            ])
            
            logger.info("Initialized prompt template")
        
        return self._prompt_template
    
    @property
    def chain(self):
        """Get or create the RAG chain."""
        if self._chain is None:
            self._chain = self.prompt_template | self.llm | StrOutputParser()
            logger.info("Initialized RAG chain")
        
        return self._chain
    
    def generate_response(self, query: str, documents: List[Document]) -> str:
        """Generate response based on query and retrieved documents."""
        try:
            logger.info(f"Generating response for query: '{query[:50]}...'")
            
            # Combine document contents
            if documents:
                doc_content = "\n\n".join([doc.page_content for doc in documents])
                logger.debug(f"Using {len(documents)} documents for context")
            else:
                doc_content = "No relevant documents found."
                logger.warning("No documents provided for context")
            
            # Generate response
            response = self.chain.invoke({
                "documents": doc_content,
                "question": query
            })
            
            logger.info("Response generated successfully")
            return response
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise ModelError(f"Failed to generate response: {e}")
    
    def is_available(self) -> bool:
        """Check if LLM service is available."""
        try:
            # Simple test to check if LLM is working
            test_response = self.llm.invoke("Test")
            return True
        except Exception as e:
            logger.warning(f"LLM service not available: {e}")
            return False


# Global LLM service instance
llm_service = LLMService()
