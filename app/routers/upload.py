"""Upload router for handling file uploads."""

from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from fastapi.responses import JSONResponse

from app.models import (
    ErrorPayload,
    ErrorResponse,
    ErrorResponseStandard,
    RequestHeader,
    UploadPayload,
    UploadResponse,
    UploadResponseStandard,
)
from app.services import document_service, vector_service
from app.utils import (
    FileProcessingError,
    VectorStoreError,
    logger,
    set_trace_id,
    validate_upload_file,
)

router = APIRouter(prefix="/upload", tags=["upload"])


@router.post(
    "/",
    response_model=UploadResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
    },
    summary="Upload and process PDF file",
    description="""
    Upload a PDF file for processing and storage in the vector database.

    This endpoint:
    1. Validates the uploaded file (type and size)
    2. Extracts text content from all pages
    3. Extracts and summarizes images using Google Generative AI
    4. Splits content into chunks for optimal retrieval
    5. Stores embeddings in the vector database

    **File Requirements:**
    - Format: PDF only
    - Maximum size: 50MB (configurable)
    - Must contain readable text or images

    **Processing Steps:**
    1. PDF text extraction using PyMuPDF
    2. Image extraction and AI-powered summarization
    3. Text chunking with overlap for context preservation
    4. Embedding generation using Cohere
    5. Vector storage in ChromaDB

    **Returns:**
    - Success message with processing statistics
    - Number of text and image chunks created
    - Total chunks stored in the database
    """,
    tags=["Document Processing"],
)
async def upload_and_parse(
    file: UploadFile = File(..., description="PDF file to upload")
):
    """Upload and process a PDF file."""

    try:
        # Validate file
        validate_upload_file(file)
        logger.info(f"Processing upload: {file.filename}")

        # Read file content
        file_bytes = await file.read()

        # Process PDF
        documents, stats = document_service.process_pdf(file_bytes, file.filename)

        # Store in vector database
        if vector_service.is_available():
            vector_service.add_documents(documents)
        else:
            vector_service.create_vectorstore(documents)

        # Prepare response
        response = UploadResponse(
            message="文档解析并入库成功",
            filename=file.filename,
            text_chunks=stats["text_chunks"],
            img_chunks=stats["img_chunks"],
            total_chunks=stats["total_chunks"],
        )

        logger.info(f"Upload completed successfully: {file.filename}")
        return response

    except (FileProcessingError, VectorStoreError) as e:
        logger.error(f"Processing error: {e}")
        raise HTTPException(
            status_code=400,
            detail={"error": str(e), "error_code": getattr(e, "error_code", None)},
        )

    except Exception as e:
        logger.error(f"Unexpected error during upload: {e}")
        raise HTTPException(
            status_code=500, detail={"error": "Internal server error", "detail": str(e)}
        )


@router.post(
    "/standard",
    response_model=UploadResponseStandard,
    responses={
        400: {"model": ErrorResponseStandard, "description": "Bad Request"},
        500: {"model": ErrorResponseStandard, "description": "Internal Server Error"},
    },
    summary="Upload and process PDF file (Standardized Format)",
    description="""
    Upload a PDF file for processing and storage in the vector database with standardized request/response format.

    **Request Format:**
    The file should be uploaded as multipart/form-data with a 'file' field.
    Additionally, you can provide a trace ID in the 'traceId' form field.

    **Response Format:**
    ```json
    {
        "header": {
            "traceId": "unique-trace-id"
        },
        "payload": {
            "message": "文档解析并入库成功",
            "filename": "document.pdf",
            "text_chunks": 25,
            "img_chunks": 3,
            "total_chunks": 28
        }
    }
    ```

    This endpoint follows the standardized two-part format with header containing traceId for request tracking.
    """,
    tags=["Document Processing (Standard)"],
)
async def upload_and_parse_standard(
    file: UploadFile = File(..., description="PDF file to upload"),
    trace_id: str = Form(
        default_factory=lambda: str(__import__("uuid").uuid4()),
        description="Trace ID for request tracking",
        alias="traceId",
    ),
):
    """Upload and process a PDF file with standardized format."""

    try:
        # Create request header
        header = RequestHeader(trace_id=trace_id)

        # Set trace ID for logging context
        set_trace_id(header.trace_id)

        # Validate file
        validate_upload_file(file)
        logger.info(f"Processing standardized upload: {file.filename}")

        # Read file content
        file_bytes = await file.read()

        # Process PDF
        documents, stats = document_service.process_pdf(file_bytes, file.filename)

        # Store in vector database
        if vector_service.is_available():
            vector_service.add_documents(documents)
        else:
            vector_service.create_vectorstore(documents)

        # Prepare response
        payload = UploadPayload(
            message="文档解析并入库成功",
            filename=file.filename,
            text_chunks=stats["text_chunks"],
            img_chunks=stats["img_chunks"],
        )

        response = UploadResponseStandard(header=header, payload=payload)

        logger.info(f"Standardized upload completed successfully: {file.filename}")
        return response

    except (FileProcessingError, VectorStoreError) as e:
        logger.error(f"Processing error: {e}")
        raise HTTPException(
            status_code=400,
            detail={
                "header": {"traceId": trace_id},
                "payload": {
                    "error": str(e),
                    "error_code": getattr(e, "error_code", None),
                },
            },
        )

    except Exception as e:
        logger.error(f"Unexpected error during standardized upload: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "header": {"traceId": trace_id},
                "payload": {"error": "Internal server error", "detail": str(e)},
            },
        )
