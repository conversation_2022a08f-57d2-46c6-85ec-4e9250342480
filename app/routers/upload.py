"""Upload router for handling file uploads."""

from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse

from app.models import UploadResponse, ErrorResponse
from app.services import document_service, vector_service
from app.utils import logger, validate_upload_file, FileProcessingError, VectorStoreError

router = APIRouter(prefix="/upload", tags=["upload"])


@router.post(
    "/",
    response_model=UploadResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
    },
    summary="Upload and process PDF file",
    description="Upload a PDF file, extract text and images, and store in vector database"
)
async def upload_and_parse(file: UploadFile = File(..., description="PDF file to upload")):
    """Upload and process a PDF file."""
    
    try:
        # Validate file
        validate_upload_file(file)
        logger.info(f"Processing upload: {file.filename}")
        
        # Read file content
        file_bytes = await file.read()
        
        # Process PDF
        documents, stats = document_service.process_pdf(file_bytes, file.filename)
        
        # Store in vector database
        if vector_service.is_available():
            vector_service.add_documents(documents)
        else:
            vector_service.create_vectorstore(documents)
        
        # Prepare response
        response = UploadResponse(
            message="文档解析并入库成功",
            filename=file.filename,
            text_chunks=stats["text_chunks"],
            img_chunks=stats["img_chunks"],
            total_chunks=stats["total_chunks"]
        )
        
        logger.info(f"Upload completed successfully: {file.filename}")
        return response
        
    except (FileProcessingError, VectorStoreError) as e:
        logger.error(f"Processing error: {e}")
        raise HTTPException(
            status_code=400,
            detail={"error": str(e), "error_code": getattr(e, 'error_code', None)}
        )
    
    except Exception as e:
        logger.error(f"Unexpected error during upload: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "detail": str(e)}
        )
