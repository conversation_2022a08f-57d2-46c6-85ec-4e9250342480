"""Query router for handling search queries."""

from fastapi import APIRouter, HTTPException, Form
from fastapi.responses import JSONResponse

from app.models import QueryRequest, QueryResponse, ErrorResponse
from app.services import vector_service, llm_service
from app.utils import logger, VectorStoreError, ModelError

router = APIRouter(prefix="/query", tags=["query"])


@router.post(
    "/",
    response_model=QueryResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
    },
    summary="Query the knowledge base",
    description="Search the vector database and generate an answer using RAG"
)
async def query_knowledge_base(query: str = Form(..., description="Query text")):
    """Query the knowledge base using RAG."""
    
    try:
        # Validate query
        query_request = QueryRequest(query=query)
        validated_query = query_request.query
        
        logger.info(f"Processing query: '{validated_query[:50]}...'")
        
        # Check if vector store is available
        if not vector_service.is_available():
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "请先上传并解析文档",
                    "error_code": "NO_DOCUMENTS"
                }
            )
        
        # Perform similarity search
        documents = vector_service.similarity_search(validated_query)
        
        if not documents:
            logger.warning("No relevant documents found")
            response = QueryResponse(
                answer="抱歉，我在已上传的文档中没有找到相关信息来回答您的问题。",
                query=validated_query,
                retrieved_chunks=0
            )
            return response
        
        # Generate response using LLM
        answer = llm_service.generate_response(validated_query, documents)
        
        # Prepare response
        response = QueryResponse(
            answer=answer,
            query=validated_query,
            retrieved_chunks=len(documents)
        )
        
        logger.info(f"Query completed successfully, retrieved {len(documents)} chunks")
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    
    except (VectorStoreError, ModelError) as e:
        logger.error(f"Service error: {e}")
        raise HTTPException(
            status_code=400,
            detail={"error": str(e), "error_code": getattr(e, 'error_code', None)}
        )
    
    except Exception as e:
        logger.error(f"Unexpected error during query: {e}")
        raise HTTPException(
            status_code=500,
            detail={"error": "Internal server error", "detail": str(e)}
        )
