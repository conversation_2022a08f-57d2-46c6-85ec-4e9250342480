"""Query router for handling search queries."""

from fastapi import <PERSON><PERSON>out<PERSON>, Form, HTTPException
from fastapi.responses import JSONResponse

from app.models import ErrorResponse, QueryRequest, QueryResponse
from app.services import llm_service, vector_service
from app.utils import <PERSON><PERSON>rror, VectorStoreError, logger

router = APIRouter(prefix="/query", tags=["query"])


@router.post(
    "/",
    response_model=QueryResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
    },
    summary="Query the knowledge base",
    description="""
    Query the knowledge base using Retrieval-Augmented Generation (RAG).

    This endpoint:
    1. Validates the input query
    2. Performs semantic search in the vector database
    3. Retrieves the most relevant document chunks
    4. Generates a contextual answer using LLM

    **Query Requirements:**
    - Length: 1-1000 characters
    - Must contain meaningful text (not just whitespace)
    - Should be a clear question or statement

    **RAG Process:**
    1. Query embedding generation using Cohere
    2. Similarity search in ChromaDB vector store
    3. Retrieval of top-k most relevant chunks (default: 3)
    4. Context-aware answer generation using Cohere LLM
    5. Response formatting and validation

    **Returns:**
    - Generated answer based on retrieved context
    - Original query for reference
    - Number of chunks used for context
    - Optional confidence score

    **Note:** Requires documents to be uploaded first via the upload endpoint.
    """,
    tags=["Question Answering"],
)
async def query_knowledge_base(query: str = Form(..., description="Query text")):
    """Query the knowledge base using RAG."""

    try:
        # Validate query
        query_request = QueryRequest(query=query)
        validated_query = query_request.query

        logger.info(f"Processing query: '{validated_query[:50]}...'")

        # Check if vector store is available
        if not vector_service.is_available():
            raise HTTPException(
                status_code=400,
                detail={"error": "请先上传并解析文档", "error_code": "NO_DOCUMENTS"},
            )

        # Perform similarity search
        documents = vector_service.similarity_search(validated_query)

        if not documents:
            logger.warning("No relevant documents found")
            response = QueryResponse(
                answer="抱歉，我在已上传的文档中没有找到相关信息来回答您的问题。",
                query=validated_query,
                retrieved_chunks=0,
            )
            return response

        # Generate response using LLM
        answer = llm_service.generate_response(validated_query, documents)

        # Prepare response
        response = QueryResponse(
            answer=answer, query=validated_query, retrieved_chunks=len(documents)
        )

        logger.info(f"Query completed successfully, retrieved {len(documents)} chunks")
        return response

    except HTTPException:
        # Re-raise HTTP exceptions
        raise

    except (VectorStoreError, ModelError) as e:
        logger.error(f"Service error: {e}")
        raise HTTPException(
            status_code=400,
            detail={"error": str(e), "error_code": getattr(e, "error_code", None)},
        )

    except Exception as e:
        logger.error(f"Unexpected error during query: {e}")
        raise HTTPException(
            status_code=500, detail={"error": "Internal server error", "detail": str(e)}
        )
