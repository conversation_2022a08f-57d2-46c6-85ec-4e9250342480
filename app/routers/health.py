"""Health check router."""

from fastapi import APIRouter

from app.config import settings
from app.models import HealthResponse
from app.services import llm_service, vector_service
from app.utils import logger

router = APIRouter(prefix="/health", tags=["health"])


@router.get(
    "/",
    response_model=HealthResponse,
    summary="Health check",
    description="""
    Check the health status of the application and its dependencies.

    This endpoint provides:
    - Overall application status
    - Application version information
    - Vector database connectivity status
    - Model loading status (embeddings and LLM)

    **Status Values:**
    - `healthy`: All systems operational
    - `unhealthy`: One or more systems have issues

    **Vector DB Status:**
    - `available`: Database is accessible and ready
    - `unavailable`: Database is not accessible
    - `error`: Database connection error

    **Use Cases:**
    - Monitoring and alerting
    - Load balancer health checks
    - Deployment verification
    - Troubleshooting connectivity issues
    """,
    tags=["System Health"],
)
async def health_check():
    """Perform health check."""

    try:
        # Check vector database status
        vector_db_status = (
            "available" if vector_service.is_available() else "unavailable"
        )

        # Check if models are loaded
        models_loaded = True
        try:
            # Test embedding model
            _ = vector_service.embedding_model
            # Test LLM (lightweight check)
            _ = llm_service.llm
        except Exception as e:
            logger.warning(f"Models not fully loaded: {e}")
            models_loaded = False

        response = HealthResponse(
            status="healthy",
            version=settings.app_version,
            vector_db_status=vector_db_status,
            models_loaded=models_loaded,
        )

        logger.debug("Health check completed")
        return response

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            version=settings.app_version,
            vector_db_status="error",
            models_loaded=False,
        )
