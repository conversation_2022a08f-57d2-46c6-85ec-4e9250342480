"""Health check router."""

from fastapi import APIRouter

from app.config import settings
from app.models import (
    HealthPayload,
    HealthResponse,
    HealthResponseStandard,
    RequestHeader,
)
from app.services import llm_service, vector_service
from app.utils import logger, set_trace_id

router = APIRouter(prefix="/health", tags=["health"])


@router.get(
    "/",
    response_model=HealthResponse,
    summary="Health check",
    description="""
    Check the health status of the application and its dependencies.

    This endpoint provides:
    - Overall application status
    - Application version information
    - Vector database connectivity status
    - Model loading status (embeddings and LLM)

    **Status Values:**
    - `healthy`: All systems operational
    - `unhealthy`: One or more systems have issues

    **Vector DB Status:**
    - `available`: Database is accessible and ready
    - `unavailable`: Database is not accessible
    - `error`: Database connection error

    **Use Cases:**
    - Monitoring and alerting
    - Load balancer health checks
    - Deployment verification
    - Troubleshooting connectivity issues
    """,
    tags=["System Health"],
)
async def health_check():
    """Perform health check."""

    try:
        # Check vector database status
        vector_db_status = (
            "available" if vector_service.is_available() else "unavailable"
        )

        # Check if models are loaded
        models_loaded = True
        try:
            # Test embedding model
            _ = vector_service.embedding_model
            # Test LLM (lightweight check)
            _ = llm_service.llm
        except Exception as e:
            logger.warning(f"Models not fully loaded: {e}")
            models_loaded = False

        response = HealthResponse(
            status="healthy",
            version=settings.app_version,
            vector_db_status=vector_db_status,
            models_loaded=models_loaded,
        )

        logger.debug("Health check completed")
        return response

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            version=settings.app_version,
            vector_db_status="error",
            models_loaded=False,
        )


@router.get(
    "/standard",
    response_model=HealthResponseStandard,
    summary="Health check (Standardized Format)",
    description="""
    Check the health status of the application and its dependencies with standardized response format.

    **Response Format:**
    ```json
    {
        "header": {
            "traceId": "unique-trace-id"
        },
        "payload": {
            "status": "healthy",
            "version": "1.0.0",
            "vector_db_status": "available",
            "models_loaded": true
        }
    }
    ```

    This endpoint follows the standardized two-part format with header containing traceId for request tracking.
    """,
    tags=["System Health (Standard)"],
)
async def health_check_standard(trace_id: str = None):
    """Perform health check with standardized format."""

    try:
        # Generate trace ID if not provided
        if not trace_id:
            import uuid

            trace_id = str(uuid.uuid4())

        # Set trace ID for logging context
        set_trace_id(trace_id)

        # Create request header
        header = RequestHeader(trace_id=trace_id)

        # Check vector database status
        vector_db_status = (
            "available" if vector_service.is_available() else "unavailable"
        )

        # Check if models are loaded
        models_loaded = True
        try:
            # Test embedding model
            _ = vector_service.embedding_model
            # Test LLM (lightweight check)
            _ = llm_service.llm
        except Exception as e:
            logger.warning(f"Models not fully loaded: {e}")
            models_loaded = False

        payload = HealthPayload(
            status="healthy",
            version=settings.app_version,
            vector_db_status=vector_db_status,
            models_loaded=models_loaded,
        )

        response = HealthResponseStandard(header=header, payload=payload)

        logger.debug("Standardized health check completed")
        return response

    except Exception as e:
        logger.error(f"Standardized health check failed: {e}")

        # Generate trace ID if not available
        if not trace_id:
            import uuid

            trace_id = str(uuid.uuid4())

        header = RequestHeader(trace_id=trace_id)
        payload = HealthPayload(
            status="unhealthy",
            version=settings.app_version,
            vector_db_status="error",
            models_loaded=False,
        )

        return HealthResponseStandard(header=header, payload=payload)
