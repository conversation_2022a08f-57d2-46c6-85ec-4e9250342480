"""Pydantic models for request/response schemas."""

from typing import Optional

from pydantic import BaseModel, Field, computed_field, field_validator


class UploadResponse(BaseModel):
    """Response model for file upload."""

    message: str = Field(..., description="Success message")
    filename: str = Field(..., description="Uploaded filename")
    text_chunks: int = Field(..., description="Number of text chunks processed")
    img_chunks: int = Field(..., description="Number of image chunks processed")

    @computed_field
    @property
    def total_chunks(self) -> int:
        """Calculate total chunks from text and image chunks."""
        return self.text_chunks + self.img_chunks


class QueryRequest(BaseModel):
    """Request model for query."""

    query: str = Field(..., min_length=1, max_length=1000, description="Query text")

    @field_validator("query")
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate query text."""
        if not v.strip():
            raise ValueError("Query cannot be empty or whitespace only")
        return v.strip()


class QueryResponse(BaseModel):
    """Response model for query."""

    answer: str = Field(..., description="Generated answer")
    query: str = Field(..., description="Original query")
    retrieved_chunks: int = Field(..., description="Number of retrieved chunks")
    confidence: Optional[float] = Field(None, description="Confidence score")


class ErrorResponse(BaseModel):
    """Error response model."""

    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    error_code: Optional[str] = Field(None, description="Error code")


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Application version")
    vector_db_status: str = Field(..., description="Vector database status")
    models_loaded: bool = Field(..., description="Whether models are loaded")
