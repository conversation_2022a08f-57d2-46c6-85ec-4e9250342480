"""Pydantic models for request/response schemas."""

import uuid
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, computed_field, field_validator


# Base request/response models for standardized format
class RequestHeader(BaseModel):
    """Standard request header."""

    trace_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique trace ID for request tracking",
        alias="traceId",
    )

    class Config:
        populate_by_name = True


class BaseRequest(BaseModel):
    """Base request model with standardized format."""

    header: RequestHeader = Field(..., description="Request header")
    payload: Dict[str, Any] = Field(..., description="Request payload")


class BaseResponse(BaseModel):
    """Base response model with standardized format."""

    header: RequestHeader = Field(..., description="Response header")
    payload: Dict[str, Any] = Field(..., description="Response payload")


# Legacy models (keeping for backward compatibility)
class UploadResponse(BaseModel):
    """Response model for file upload."""

    message: str = Field(..., description="Success message")
    filename: str = Field(..., description="Uploaded filename")
    text_chunks: int = Field(..., description="Number of text chunks processed")
    img_chunks: int = Field(..., description="Number of image chunks processed")

    @computed_field
    @property
    def total_chunks(self) -> int:
        """Calculate total chunks from text and image chunks."""
        return self.text_chunks + self.img_chunks


class QueryRequest(BaseModel):
    """Request model for query."""

    query: str = Field(..., min_length=1, max_length=1000, description="Query text")

    @field_validator("query")
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate query text."""
        if not v.strip():
            raise ValueError("Query cannot be empty or whitespace only")
        return v.strip()


class QueryResponse(BaseModel):
    """Response model for query."""

    answer: str = Field(..., description="Generated answer")
    query: str = Field(..., description="Original query")
    retrieved_chunks: int = Field(..., description="Number of retrieved chunks")
    confidence: Optional[float] = Field(None, description="Confidence score")


class ErrorResponse(BaseModel):
    """Error response model."""

    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    error_code: Optional[str] = Field(None, description="Error code")


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Application version")
    vector_db_status: str = Field(..., description="Vector database status")
    models_loaded: bool = Field(..., description="Whether models are loaded")


# New standardized request/response models
class QueryPayload(BaseModel):
    """Payload for query request."""

    query: str = Field(..., min_length=1, max_length=1000, description="Query text")

    @field_validator("query")
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate query text."""
        if not v.strip():
            raise ValueError("Query cannot be empty or whitespace only")
        return v.strip()


class QueryRequestStandard(BaseModel):
    """Standardized query request model."""

    header: RequestHeader = Field(..., description="Request header")
    payload: QueryPayload = Field(..., description="Query payload")


class UploadPayload(BaseModel):
    """Payload for upload response."""

    message: str = Field(..., description="Success message")
    filename: str = Field(..., description="Uploaded filename")
    text_chunks: int = Field(..., description="Number of text chunks processed")
    img_chunks: int = Field(..., description="Number of image chunks processed")

    @computed_field
    @property
    def total_chunks(self) -> int:
        """Calculate total chunks from text and image chunks."""
        return self.text_chunks + self.img_chunks


class UploadResponseStandard(BaseModel):
    """Standardized upload response model."""

    header: RequestHeader = Field(..., description="Response header")
    payload: UploadPayload = Field(..., description="Upload result payload")


class QueryResponsePayload(BaseModel):
    """Payload for query response."""

    answer: str = Field(..., description="Generated answer")
    query: str = Field(..., description="Original query")
    retrieved_chunks: int = Field(..., description="Number of retrieved chunks")
    confidence: Optional[float] = Field(None, description="Confidence score")


class QueryResponseStandard(BaseModel):
    """Standardized query response model."""

    header: RequestHeader = Field(..., description="Response header")
    payload: QueryResponsePayload = Field(..., description="Query result payload")


class ErrorPayload(BaseModel):
    """Payload for error response."""

    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    error_code: Optional[str] = Field(None, description="Error code")


class HealthPayload(BaseModel):
    """Payload for health response."""

    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Application version")
    vector_db_status: str = Field(..., description="Vector database status")
    models_loaded: bool = Field(..., description="Whether models are loaded")


class HealthResponseStandard(BaseModel):
    """Standardized health response model."""

    header: RequestHeader = Field(..., description="Response header")
    payload: HealthPayload = Field(..., description="Health status payload")
