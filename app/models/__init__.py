"""Models module."""

from .schemas import (  # Legacy models (for backward compatibility); New standardized models
    BaseRequest,
    BaseResponse,
    ErrorResponse,
    HealthPayload,
    HealthResponse,
    HealthResponseStandard,
    QueryPayload,
    QueryRequest,
    QueryRequestStandard,
    QueryResponse,
    QueryResponsePayload,
    QueryResponseStandard,
    RequestHeader,
    UploadPayload,
    UploadResponse,
    UploadResponseStandard,
)

__all__ = [
    # Legacy models
    "UploadResponse",
    "QueryRequest",
    "QueryResponse",
    "ErrorResponse",
    "HealthResponse",
    # New standardized models
    "RequestHeader",
    "BaseRequest",
    "BaseResponse",
    "QueryRequestStandard",
    "QueryResponseStandard",
    "UploadResponseStandard",
    "HealthResponseStandard",
    "QueryPayload",
    "UploadPayload",
    "QueryResponsePayload",
    "HealthPayload",
]
