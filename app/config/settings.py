"""Application configuration settings."""

from typing import List

from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Application
    app_name: str = "Multi-Modal RAG API"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"

    # API Keys
    cohere_api_key: str
    google_api_key: str

    # Vector Database
    vector_db_dir: str = "chroma_db"
    collection_name: str = "multi_model_rag"

    # File Upload
    max_file_size_mb: int = 50
    allowed_file_types: List[str] = ["pdf"]
    upload_dir: str = "static/uploads"
    extracted_images_dir: str = "static/images"

    # Model Settings
    embedding_model: str = "embed-english-v3.0"
    llm_model: str = "command-r-plus"
    llm_temperature: float = 0.0
    chunk_size: int = 400
    chunk_overlap: int = 50
    retrieval_k: int = 3

    @field_validator("max_file_size_mb")
    def validate_max_file_size(cls, v):
        if v <= 0:
            raise ValueError("max_file_size_mb must be positive")
        return v

    @field_validator("chunk_size")
    def validate_chunk_size(cls, v):
        if v <= 0:
            raise ValueError("chunk_size must be positive")
        return v

    @field_validator("retrieval_k")
    def validate_retrieval_k(cls, v):
        if v <= 0:
            raise ValueError("retrieval_k must be positive")
        return v

    @property
    def max_file_size_bytes(self) -> int:
        """Convert MB to bytes."""
        return self.max_file_size_mb * 1024 * 1024

    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
