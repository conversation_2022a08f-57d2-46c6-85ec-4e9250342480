#!/usr/bin/env python3
"""
API Test Script for Multi-Modal RAG Application (Standardized Format)

This script tests the new standardized API endpoints with the two-part request/response format.
"""

import requests
import json
import time
import sys
import uuid
from pathlib import Path


class StandardAPITester:
    """Test class for standardized API endpoints."""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def generate_trace_id(self):
        """Generate a unique trace ID."""
        return str(uuid.uuid4())
    
    def test_health_endpoint_standard(self):
        """Test the standardized health check endpoint."""
        print("🔍 Testing Standardized Health Endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/health/standard")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Standardized health check passed")
                print(f"   Trace ID: {data.get('header', {}).get('traceId')}")
                payload = data.get('payload', {})
                print(f"   Status: {payload.get('status')}")
                print(f"   Version: {payload.get('version')}")
                print(f"   Vector DB: {payload.get('vector_db_status')}")
                print(f"   Models Loaded: {payload.get('models_loaded')}")
                return True
            else:
                print(f"❌ Standardized health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Standardized health check error: {e}")
            return False
    
    def test_upload_endpoint_standard(self, pdf_path=None):
        """Test the standardized upload endpoint."""
        print("\n🔍 Testing Standardized Upload Endpoint...")
        
        # Use default test file if none provided
        if pdf_path is None:
            pdf_path = "resource/pdf/attention_is_all_you_need.pdf"
        
        if not Path(pdf_path).exists():
            print(f"❌ Test PDF file not found: {pdf_path}")
            return False
        
        try:
            trace_id = self.generate_trace_id()
            
            with open(pdf_path, "rb") as f:
                files = {"file": (Path(pdf_path).name, f, "application/pdf")}
                data = {"traceId": trace_id}
                
                print(f"   Uploading: {Path(pdf_path).name}")
                print(f"   Trace ID: {trace_id}")
                
                response = self.session.post(
                    f"{self.base_url}/upload/standard",
                    files=files,
                    data=data,
                    timeout=120  # Allow time for processing
                )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Standardized upload successful")
                
                header = result.get('header', {})
                payload = result.get('payload', {})
                
                print(f"   Response Trace ID: {header.get('traceId')}")
                print(f"   Filename: {payload.get('filename')}")
                print(f"   Text chunks: {payload.get('text_chunks')}")
                print(f"   Image chunks: {payload.get('img_chunks')}")
                print(f"   Total chunks: {payload.get('total_chunks')}")
                return True
            else:
                print(f"❌ Standardized upload failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Standardized upload error: {e}")
            return False
    
    def test_query_endpoint_standard(self, query="What is this document about?"):
        """Test the standardized query endpoint."""
        print("\n🔍 Testing Standardized Query Endpoint...")
        
        try:
            trace_id = self.generate_trace_id()
            
            request_data = {
                "header": {
                    "traceId": trace_id
                },
                "payload": {
                    "query": query
                }
            }
            
            print(f"   Query: {query}")
            print(f"   Trace ID: {trace_id}")
            
            response = self.session.post(
                f"{self.base_url}/query/standard",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Standardized query successful")
                
                header = result.get('header', {})
                payload = result.get('payload', {})
                
                print(f"   Response Trace ID: {header.get('traceId')}")
                print(f"   Answer: {payload.get('answer', '')[:100]}...")
                print(f"   Retrieved chunks: {payload.get('retrieved_chunks')}")
                return True
            else:
                print(f"❌ Standardized query failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Standardized query error: {e}")
            return False
    
    def test_error_handling_standard(self):
        """Test error handling with standardized format."""
        print("\n🔍 Testing Standardized Error Handling...")
        
        tests_passed = 0
        total_tests = 2
        
        # Test 1: Invalid query request
        try:
            trace_id = self.generate_trace_id()
            request_data = {
                "header": {
                    "traceId": trace_id
                },
                "payload": {
                    "query": ""  # Empty query should fail validation
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/query/standard",
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 422:  # Validation error
                print("✅ Empty query properly rejected in standardized format")
                tests_passed += 1
            else:
                print(f"❌ Empty query not properly handled: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing empty query: {e}")
        
        # Test 2: Malformed request
        try:
            response = self.session.post(
                f"{self.base_url}/query/standard",
                json={"invalid": "request"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 422:  # Validation error
                print("✅ Malformed request properly rejected")
                tests_passed += 1
            else:
                print(f"❌ Malformed request not properly handled: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing malformed request: {e}")
        
        return tests_passed == total_tests
    
    def test_trace_id_consistency(self):
        """Test that trace IDs are consistent throughout the request."""
        print("\n🔍 Testing Trace ID Consistency...")
        
        try:
            trace_id = self.generate_trace_id()
            
            # Test health endpoint
            response = self.session.get(f"{self.base_url}/health/standard")
            
            if response.status_code == 200:
                data = response.json()
                response_trace_id = data.get('header', {}).get('traceId')
                
                if response_trace_id:
                    print(f"✅ Trace ID generated and returned: {response_trace_id}")
                    return True
                else:
                    print("❌ No trace ID in response")
                    return False
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Trace ID consistency test error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all standardized API tests."""
        print("🚀 Starting Standardized API Tests for Multi-Modal RAG Application")
        print("=" * 70)
        
        tests = [
            ("Standardized Health Check", self.test_health_endpoint_standard),
            ("Trace ID Consistency", self.test_trace_id_consistency),
            ("Standardized Upload", self.test_upload_endpoint_standard),
            ("Standardized Query", self.test_query_endpoint_standard),
            ("Standardized Error Handling", self.test_error_handling_standard),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ Test '{test_name}' failed with exception: {e}")
                results[test_name] = False
            
            time.sleep(1)  # Brief pause between tests
        
        # Summary
        print("\n" + "=" * 70)
        print("📊 STANDARDIZED API TEST SUMMARY")
        print("=" * 70)
        
        passed = 0
        total = len(tests)
        
        for test_name, result in results.items():
            status = "PASSED" if result else "FAILED"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL STANDARDIZED API TESTS PASSED! The new format is working correctly.")
            return True
        else:
            print("❌ Some standardized API tests failed. Please check the application and logs.")
            return False


def main():
    """Main function to run standardized API tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Multi-Modal RAG API standardized endpoints")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API (default: http://localhost:8000)")
    parser.add_argument("--pdf", help="Path to PDF file for testing upload")
    
    args = parser.parse_args()
    
    # Check if server is running
    try:
        response = requests.get(args.url, timeout=5)
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to API at {args.url}")
        print("   Make sure the application is running with: python main_new.py")
        sys.exit(1)
    
    # Run tests
    tester = StandardAPITester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
